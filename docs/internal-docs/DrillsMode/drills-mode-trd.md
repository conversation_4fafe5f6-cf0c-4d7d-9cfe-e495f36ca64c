# Technical Requirements Document: Drills Mode

## 1. Technical Overview

The Drills Mode feature will enhance the Stack Advantage blackjack trainer by providing focused practice exercises specifically designed to improve card counting speed and accuracy. This document outlines the technical implementation details required to deliver this feature, focusing on architecture, data structures, components, and integration points.

## 2. Architecture and Implementation Approach

### 2.1 Component Architecture

The implementation will follow the existing architecture pattern:
- Create a new `DrillsEngine` class to handle drill logic and state management
- Define new types and interfaces for drill configuration and results
- Create new UI components for drill selection, execution, and results display
- Implement a new `useDrills` hook for React state management
- Integrate with existing game components where appropriate

### 2.2 Implementation Strategy

1. Define data structures for drills configuration and results
2. Implement the core `DrillsEngine` class
3. Create the `useDrills` React hook
4. Develop UI components for the drills experience
5. Implement performance tracking and storage
6. Add educational content and tooltips
7. Integrate with the main application
8. Implement automated testing

## 3. Data Structure Modifications

### 3.1 Drill Types and Configuration

Create new types in `src/types/drills.ts`:

```typescript
/** Available drill types */
export type DrillType =
  | 'runningCount'
  | 'trueCount'
  | 'speed'
  | 'distraction'
  | 'deckEstimation'
  | 'spotCheck';

/** Difficulty levels for drills */
export type DrillDifficulty = 'beginner' | 'intermediate' | 'advanced' | 'expert';

/** Speed settings for card display */
export type CardSpeed = 'slow' | 'medium' | 'fast' | 'expert';

/** Configuration for a drill session */
export interface DrillConfig {
  /** Type of drill */
  type: DrillType;

  /** Difficulty level */
  difficulty: DrillDifficulty;

  /** Duration in seconds (0 for non-timed drills) */
  duration: number;

  /** Speed of card display */
  cardSpeed: CardSpeed;

  /** Whether to show visual hints */
  showHints: boolean;

  /** Number of decks to use */
  deckCount: number;

  /** Custom options specific to each drill type */
  options: Record<string, any>;
}

/** Saved drill configuration with name */
export interface SavedDrillConfig extends DrillConfig {
  /** User-defined name for this configuration */
  name: string;

  /** Date when this configuration was saved */
  savedAt: string;
}
```

### 3.2 Drill Results and Performance

Add types for tracking drill results:

```typescript
/** Results from a single drill session */
export interface DrillResult {
  /** ID of this drill session */
  id: string;

  /** Configuration used for this session */
  config: DrillConfig;

  /** When the drill was started */
  startTime: string;

  /** When the drill was completed */
  endTime: string;

  /** Overall accuracy percentage */
  accuracy: number;

  /** Cards processed per minute */
  cardsPerMinute: number;

  /** Average response time in milliseconds */
  avgResponseTime: number;

  /** Detailed results for each interaction */
  details: DrillInteractionResult[];

  /** Achievement badges earned in this session */
  badgesEarned: string[];

  /** Points earned in this session */
  pointsEarned: number;
}

/** Result of a single interaction within a drill */
export interface DrillInteractionResult {
  /** Timestamp of this interaction */
  timestamp: number;

  /** Cards shown in this interaction */
  cards: Card[];

  /** Expected count after these cards */
  expectedCount: number;

  /** User's count input */
  userCount: number;

  /** Whether the user's input was correct */
  isCorrect: boolean;

  /** Response time in milliseconds */
  responseTime: number;
}

/** User's drill performance history */
export interface DrillPerformanceHistory {
  /** Results of all completed drill sessions */
  sessions: DrillResult[];

  /** Performance metrics by drill type */
  byDrillType: Record<DrillType, DrillTypePerformance>;

  /** Achievement badges earned */
  badges: string[];

  /** Total points earned across all drills */
  totalPoints: number;

  /** Current user level */
  level: number;
}

/** Performance metrics for a specific drill type */
export interface DrillTypePerformance {
  /** Number of sessions completed */
  sessionsCompleted: number;

  /** Best accuracy achieved */
  bestAccuracy: number;

  /** Best speed achieved (cards per minute) */
  bestSpeed: number;

  /** Average accuracy across all sessions */
  avgAccuracy: number;

  /** Average speed across all sessions */
  avgSpeed: number;

  /** Date of last session */
  lastPlayed: string;
}
```

### 3.3 Constants Updates

Add drill-related constants in `src/lib/constants.ts`:

```typescript
export const DRILL_CONSTANTS = {
  /** Card display speeds in milliseconds */
  CARD_SPEEDS: {
    SLOW: 2000,
    MEDIUM: 1000,
    FAST: 500,
    EXPERT: 250
  },

  /** Default durations in seconds */
  DEFAULT_DURATIONS: {
    BEGINNER: 60,
    INTERMEDIATE: 120,
    ADVANCED: 180,
    EXPERT: 300
  },

  /** Points awarded for different achievements */
  POINTS: {
    PERFECT_ACCURACY: 100,
    SPEED_THRESHOLD: 50,
    COMPLETION: 10,
    CORRECT_ANSWER: 1
  },

  /** Achievement badge definitions */
  BADGES: {
    PERFECT_NOVICE: {
      id: 'perfect_novice',
      name: 'Perfect Novice',
      description: 'Complete a beginner drill with 100% accuracy',
      icon: '🎯'
    },
    SPEED_DEMON: {
      id: 'speed_demon',
      name: 'Speed Demon',
      description: 'Process more than 100 cards per minute with at least 90% accuracy',
      icon: '⚡'
    },
    // Additional badges...
  },

  /** Default drill configurations */
  DEFAULT_CONFIGS: {
    RUNNING_COUNT_BEGINNER: {
      type: 'runningCount' as DrillType,
      difficulty: 'beginner' as DrillDifficulty,
      duration: 60,
      cardSpeed: 'slow' as CardSpeed,
      showHints: true,
      deckCount: 1,
      options: {
        pauseAfterCards: 5
      }
    },
    // Additional default configurations...
  }
} as const;
```

## 4. DrillsEngine Class

Create a new class in `src/lib/drills/drills-engine.ts`:

```typescript
/**
 * Core engine for managing drill sessions
 */
export class DrillsEngine {
  /** Current drill configuration */
  private config: DrillConfig;

  /** Cards to be used in this drill */
  private cards: Card[];

  /** Current position in the card sequence */
  private currentPosition: number;

  /** Current running count */
  private runningCount: number;

  /** Current true count */
  private trueCount: number;

  /** Remaining decks */
  private remainingDecks: number;

  /** Start time of the current drill */
  private startTime: number;

  /** Results of interactions in this drill */
  private interactionResults: DrillInteractionResult[];

  /** Whether the drill is currently active */
  private isActive: boolean;

  /** Timer ID for timed drills */
  private timerId: number | null;

  /**
   * Creates a new DrillsEngine instance
   * @param config Initial drill configuration
   */
  constructor(config: DrillConfig) {
    this.config = { ...config };
    this.cards = [];
    this.currentPosition = 0;
    this.runningCount = 0;
    this.trueCount = 0;
    this.remainingDecks = config.deckCount;
    this.startTime = 0;
    this.interactionResults = [];
    this.isActive = false;
    this.timerId = null;

    // Initialize cards for the drill
    this.initializeCards();
  }

  /**
   * Initializes the card deck(s) for this drill
   * @private
   */
  private initializeCards(): void {
    // Create and shuffle deck(s) based on configuration
    const deck = createDeck(this.config.deckCount);
    this.cards = shuffleDeck(deck);

    // For certain drill types, we may want to arrange cards in a specific way
    if (this.config.type === 'spotCheck') {
      this.arrangeSpotCheckCards();
    }
  }

  /**
   * Starts the drill session
   * @public
   */
  public start(): void {
    if (this.isActive) return;

    this.isActive = true;
    this.startTime = Date.now();
    this.interactionResults = [];
    this.currentPosition = 0;
    this.runningCount = 0;
    this.trueCount = 0;
    this.remainingDecks = this.config.deckCount;

    // Set up timer for timed drills
    if (this.config.duration > 0) {
      this.timerId = window.setTimeout(() => this.end(), this.config.duration * 1000);
    }
  }

  /**
   * Ends the current drill session
   * @public
   */
  public end(): void {
    if (!this.isActive) return;

    this.isActive = false;

    // Clear timer if it exists
    if (this.timerId !== null) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
  }

  /**
   * Gets the next card(s) to display
   * @param count Number of cards to get
   * @returns Array of cards or null if drill is complete
   * @public
   */
  public getNextCards(count: number = 1): Card[] | null {
    if (!this.isActive || this.currentPosition >= this.cards.length) {
      return null;
    }

    const endPosition = Math.min(this.currentPosition + count, this.cards.length);
    const cards = this.cards.slice(this.currentPosition, endPosition);
    this.currentPosition = endPosition;

    // Update counts
    this.updateCounts(cards);

    // Check if we've reached the end
    if (this.currentPosition >= this.cards.length) {
      this.end();
    }

    return cards;
  }

  /**
   * Updates running count and true count based on cards
   * @param cards Cards to count
   * @private
   */
  private updateCounts(cards: Card[]): void {
    // Update running count
    for (const card of cards) {
      this.runningCount += getCardValue(card);
    }

    // Update remaining decks and true count
    this.remainingDecks = (this.cards.length - this.currentPosition) / 52;
    this.trueCount = this.remainingDecks > 0
      ? Math.round((this.runningCount / this.remainingDecks) * 2) / 2
      : this.runningCount;
  }

  /**
   * Records a user interaction result
   * @param userCount User's count input
   * @returns Result of this interaction
   * @public
   */
  public recordInteraction(userCount: number): DrillInteractionResult {
    const expectedCount = this.config.type === 'trueCount' ? this.trueCount : this.runningCount;
    const isCorrect = userCount === expectedCount;
    const responseTime = Date.now() - this.startTime;

    // Get cards shown in this interaction
    const interactionCards = this.getInteractionCards();

    const result: DrillInteractionResult = {
      timestamp: Date.now(),
      cards: interactionCards,
      expectedCount,
      userCount,
      isCorrect,
      responseTime
    };

    this.interactionResults.push(result);
    return result;
  }

  /**
   * Gets the cards shown in the current interaction
   * @returns Array of cards
   * @private
   */
  private getInteractionCards(): Card[] {
    // Implementation depends on drill type
    // For most drills, this will be the cards since the last interaction
    // For spot check drills, this might be a specific subset
    return [];
  }

  /**
   * Gets the current drill result
   * @returns Current drill result
   * @public
   */
  public getResult(): DrillResult {
    const endTime = this.isActive ? Date.now() : this.startTime;
    const durationMs = endTime - this.startTime;
    const durationMinutes = durationMs / 60000;

    // Calculate accuracy
    const correctInteractions = this.interactionResults.filter(r => r.isCorrect).length;
    const accuracy = this.interactionResults.length > 0
      ? (correctInteractions / this.interactionResults.length) * 100
      : 0;

    // Calculate cards per minute
    const totalCards = this.interactionResults.reduce((sum, r) => sum + r.cards.length, 0);
    const cardsPerMinute = durationMinutes > 0 ? totalCards / durationMinutes : 0;

    // Calculate average response time
    const totalResponseTime = this.interactionResults.reduce((sum, r) => sum + r.responseTime, 0);
    const avgResponseTime = this.interactionResults.length > 0
      ? totalResponseTime / this.interactionResults.length
      : 0;

    // Determine badges earned
    const badgesEarned = this.calculateEarnedBadges(accuracy, cardsPerMinute);

    // Calculate points earned
    const pointsEarned = this.calculatePointsEarned(accuracy, cardsPerMinute, correctInteractions);

    return {
      id: generateUniqueId(),
      config: this.config,
      startTime: new Date(this.startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      accuracy,
      cardsPerMinute,
      avgResponseTime,
      details: [...this.interactionResults],
      badgesEarned,
      pointsEarned
    };
  }

  /**
   * Calculates badges earned in this session
   * @param accuracy Accuracy percentage
   * @param cardsPerMinute Cards processed per minute
   * @returns Array of badge IDs earned
   * @private
   */
  private calculateEarnedBadges(accuracy: number, cardsPerMinute: number): string[] {
    const badges: string[] = [];

    // Perfect accuracy badge
    if (accuracy === 100 && this.interactionResults.length >= 10) {
      if (this.config.difficulty === 'beginner') {
        badges.push('perfect_novice');
      } else if (this.config.difficulty === 'intermediate') {
        badges.push('perfect_intermediate');
      } else if (this.config.difficulty === 'advanced') {
        badges.push('perfect_advanced');
      } else if (this.config.difficulty === 'expert') {
        badges.push('perfect_expert');
      }
    }

    // Speed badge
    if (cardsPerMinute >= 100 && accuracy >= 90) {
      badges.push('speed_demon');
    }

    return badges;
  }

  /**
   * Calculates points earned in this session
   * @param accuracy Accuracy percentage
   * @param cardsPerMinute Cards processed per minute
   * @param correctAnswers Number of correct answers
   * @returns Total points earned
   * @private
   */
  private calculatePointsEarned(
    accuracy: number,
    cardsPerMinute: number,
    correctAnswers: number
  ): number {
    let points = 0;

    // Points for completion
    points += DRILL_CONSTANTS.POINTS.COMPLETION;

    // Points for correct answers
    points += correctAnswers * DRILL_CONSTANTS.POINTS.CORRECT_ANSWER;

    // Points for perfect accuracy
    if (accuracy === 100 && this.interactionResults.length >= 10) {
      points += DRILL_CONSTANTS.POINTS.PERFECT_ACCURACY;
    }

    // Points for speed
    if (cardsPerMinute >= 100 && accuracy >= 90) {
      points += DRILL_CONSTANTS.POINTS.SPEED_THRESHOLD;
    }

    return points;
  }
}
```

## 5. React Hook Implementation

Create a new hook in `src/hooks/useDrills.ts`:

```typescript
/**
 * React hook for managing drill state
 */
export function useDrills() {
  // State for drill engine
  const [drillEngine, setDrillEngine] = useState<DrillsEngine | null>(null);

  // State for current drill configuration
  const [config, setConfig] = useState<DrillConfig>(DRILL_CONSTANTS.DEFAULT_CONFIGS.RUNNING_COUNT_BEGINNER);

  // State for drill status
  const [isActive, setIsActive] = useState(false);

  // State for current cards being shown
  const [currentCards, setCurrentCards] = useState<Card[]>([]);

  // State for drill results
  const [results, setResults] = useState<DrillResult | null>(null);

  // State for saved configurations
  const [savedConfigs, setSavedConfigs] = useState<SavedDrillConfig[]>([]);

  // State for performance history
  const [performanceHistory, setPerformanceHistory] = useState<DrillPerformanceHistory>({
    sessions: [],
    byDrillType: {} as Record<DrillType, DrillTypePerformance>,
    badges: [],
    totalPoints: 0,
    level: 1
  });

  // Initialize drill engine when configuration changes
  useEffect(() => {
    if (!isActive && (!drillEngine || !isEqual(drillEngine.config, config))) {
      setDrillEngine(new DrillsEngine(config));
    }
  }, [config, isActive, drillEngine]);

  // Start a drill session
  const startDrill = useCallback(() => {
    if (!drillEngine) return;

    drillEngine.start();
    setIsActive(true);
    setResults(null);

    // Show first cards
    const cards = drillEngine.getNextCards(config.options.cardsPerRound || 1);
    if (cards) {
      setCurrentCards(cards);
    }
  }, [drillEngine, config]);

  // End a drill session
  const endDrill = useCallback(() => {
    if (!drillEngine || !isActive) return;

    drillEngine.end();
    setIsActive(false);

    // Get and save results
    const result = drillEngine.getResult();
    setResults(result);

    // Update performance history
    updatePerformanceHistory(result);
  }, [drillEngine, isActive]);

  // Submit user's count
  const submitCount = useCallback((userCount: number) => {
    if (!drillEngine || !isActive) return null;

    // Record interaction
    const result = drillEngine.recordInteraction(userCount);

    // Get next cards
    const nextCards = drillEngine.getNextCards(config.options.cardsPerRound || 1);
    if (nextCards) {
      setCurrentCards(nextCards);
    } else {
      // End drill if no more cards
      endDrill();
    }

    return result;
  }, [drillEngine, isActive, config, endDrill]);

  // Update performance history with new result
  const updatePerformanceHistory = useCallback((result: DrillResult) => {
    setPerformanceHistory(prev => {
      // Add session to history
      const sessions = [...prev.sessions, result];

      // Update drill type performance
      const drillType = result.config.type;
      const typePerformance = prev.byDrillType[drillType] || {
        sessionsCompleted: 0,
        bestAccuracy: 0,
        bestSpeed: 0,
        avgAccuracy: 0,
        avgSpeed: 0,
        lastPlayed: ''
      };

      const updatedTypePerformance: DrillTypePerformance = {
        sessionsCompleted: typePerformance.sessionsCompleted + 1,
        bestAccuracy: Math.max(typePerformance.bestAccuracy, result.accuracy),
        bestSpeed: Math.max(typePerformance.bestSpeed, result.cardsPerMinute),
        avgAccuracy: calculateAverage(
          typePerformance.avgAccuracy,
          result.accuracy,
          typePerformance.sessionsCompleted
        ),
        avgSpeed: calculateAverage(
          typePerformance.avgSpeed,
          result.cardsPerMinute,
          typePerformance.sessionsCompleted
        ),
        lastPlayed: result.endTime
      };

      // Update badges
      const badges = [...new Set([...prev.badges, ...result.badgesEarned])];

      // Update total points
      const totalPoints = prev.totalPoints + result.pointsEarned;

      // Calculate level based on points
      const level = Math.floor(totalPoints / 1000) + 1;

      return {
        sessions,
        byDrillType: {
          ...prev.byDrillType,
          [drillType]: updatedTypePerformance
        },
        badges,
        totalPoints,
        level
      };
    });
  }, []);

  // Save a drill configuration
  const saveConfig = useCallback((name: string) => {
    const savedConfig: SavedDrillConfig = {
      ...config,
      name,
      savedAt: new Date().toISOString()
    };

    setSavedConfigs(prev => [...prev, savedConfig]);
    return savedConfig;
  }, [config]);

  // Load a saved configuration
  const loadConfig = useCallback((savedConfig: SavedDrillConfig) => {
    setConfig(savedConfig);
  }, []);

  // Delete a saved configuration
  const deleteConfig = useCallback((savedConfig: SavedDrillConfig) => {
    setSavedConfigs(prev => prev.filter(c => c.name !== savedConfig.name));
  }, []);

  return {
    // State
    config,
    setConfig,
    isActive,
    currentCards,
    results,
    savedConfigs,
    performanceHistory,

    // Actions
    startDrill,
    endDrill,
    submitCount,
    saveConfig,
    loadConfig,
    deleteConfig
  };
}

/**
 * Helper function to calculate running average
 */
function calculateAverage(
  currentAvg: number,
  newValue: number,
  currentCount: number
): number {
  return ((currentAvg * currentCount) + newValue) / (currentCount + 1);
}
```

## 6. UI Components

### 6.1 DrillsPage Component

Create a new page component in `src/app/drills/page.tsx`:

```typescript
/**
 * Main page for Drills Mode
 */
export default function DrillsPage() {
  const {
    config,
    setConfig,
    isActive,
    currentCards,
    results,
    savedConfigs,
    performanceHistory,
    startDrill,
    endDrill,
    submitCount,
    saveConfig,
    loadConfig,
    deleteConfig
  } = useDrills();

  // State for user input
  const [userCount, setUserCount] = useState<string>('');

  // State for UI view
  const [view, setView] = useState<'select' | 'active' | 'results'>('select');

  // Effect to update view based on drill state
  useEffect(() => {
    if (isActive) {
      setView('active');
    } else if (results) {
      setView('results');
    } else {
      setView('select');
    }
  }, [isActive, results]);

  // Handle count submission
  const handleSubmitCount = () => {
    const count = parseInt(userCount);
    if (isNaN(count)) return;

    submitCount(count);
    setUserCount('');
  };

  // Render appropriate view
  return (
    <div className="drills-page">
      <h1>Card Counting Drills</h1>

      {view === 'select' && (
        <DrillSelector
          config={config}
          setConfig={setConfig}
          savedConfigs={savedConfigs}
          performanceHistory={performanceHistory}
          onStart={startDrill}
          onSave={saveConfig}
          onLoad={loadConfig}
          onDelete={deleteConfig}
        />
      )}

      {view === 'active' && (
        <ActiveDrill
          config={config}
          cards={currentCards}
          userCount={userCount}
          setUserCount={setUserCount}
          onSubmit={handleSubmitCount}
          onEnd={endDrill}
        />
      )}

      {view === 'results' && results && (
        <DrillResults
          results={results}
          performanceHistory={performanceHistory}
          onRestart={() => startDrill()}
          onNewDrill={() => setResults(null)}
        />
      )}
    </div>
  );
}
```

### 6.2 DrillSelector Component

Create `src/components/drills/DrillSelector.tsx`:

```typescript
/**
 * Component for selecting and configuring drills
 */
interface DrillSelectorProps {
  config: DrillConfig;
  setConfig: (config: DrillConfig) => void;
  savedConfigs: SavedDrillConfig[];
  performanceHistory: DrillPerformanceHistory;
  onStart: () => void;
  onSave: (name: string) => void;
  onLoad: (config: SavedDrillConfig) => void;
  onDelete: (config: SavedDrillConfig) => void;
}

export function DrillSelector({
  config,
  setConfig,
  savedConfigs,
  performanceHistory,
  onStart,
  onSave,
  onLoad,
  onDelete
}: DrillSelectorProps) {
  // Implementation details...
}
```

### 6.3 ActiveDrill Component

Create `src/components/drills/ActiveDrill.tsx`:

```typescript
/**
 * Component for the active drill session
 */
interface ActiveDrillProps {
  config: DrillConfig;
  cards: Card[];
  userCount: string;
  setUserCount: (value: string) => void;
  onSubmit: () => void;
  onEnd: () => void;
}

export function ActiveDrill({
  config,
  cards,
  userCount,
  setUserCount,
  onSubmit,
  onEnd
}: ActiveDrillProps) {
  // Implementation details...
}
```

### 6.4 DrillResults Component

Create `src/components/drills/DrillResults.tsx`:

```typescript
/**
 * Component for displaying drill results
 */
interface DrillResultsProps {
  results: DrillResult;
  performanceHistory: DrillPerformanceHistory;
  onRestart: () => void;
  onNewDrill: () => void;
}

export function DrillResults({
  results,
  performanceHistory,
  onRestart,
  onNewDrill
}: DrillResultsProps) {
  // Implementation details...
}
```

### 6.5 PerformanceChart Component

Create `src/components/drills/PerformanceChart.tsx`:

```typescript
/**
 * Component for displaying performance charts and analytics
 */
interface PerformanceChartProps {
  performanceHistory: DrillPerformanceHistory;
  drillType?: DrillType;
  timeRange?: 'week' | 'month' | 'all';
}

export function PerformanceChart({
  performanceHistory,
  drillType,
  timeRange = 'month'
}: PerformanceChartProps) {
  const [chartType, setChartType] = useState<'accuracy' | 'speed' | 'progress'>('accuracy');

  // Filter sessions based on drill type and time range
  const filteredSessions = useMemo(() => {
    let sessions = performanceHistory.sessions;

    if (drillType) {
      sessions = sessions.filter(s => s.config.type === drillType);
    }

    const now = new Date();
    const cutoffDate = new Date();

    switch (timeRange) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      default:
        cutoffDate.setFullYear(2000); // Show all
    }

    return sessions.filter(s => new Date(s.startTime) >= cutoffDate);
  }, [performanceHistory.sessions, drillType, timeRange]);

  // Prepare chart data
  const chartData = useMemo(() => {
    return filteredSessions.map(session => ({
      date: new Date(session.startTime).toLocaleDateString(),
      accuracy: session.accuracy,
      speed: session.cardsPerMinute,
      points: session.pointsEarned
    }));
  }, [filteredSessions]);

  return (
    <div className="performance-chart">
      <div className="chart-controls">
        <select
          value={chartType}
          onChange={(e) => setChartType(e.target.value as any)}
        >
          <option value="accuracy">Accuracy Over Time</option>
          <option value="speed">Speed Over Time</option>
          <option value="progress">Progress Points</option>
        </select>
      </div>

      <div className="chart-container">
        {/* Chart implementation using a library like recharts or chart.js */}
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey={chartType}
              stroke="#8884d8"
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      <div className="chart-summary">
        <div className="stat">
          <span className="label">Sessions:</span>
          <span className="value">{filteredSessions.length}</span>
        </div>
        <div className="stat">
          <span className="label">Avg Accuracy:</span>
          <span className="value">
            {filteredSessions.length > 0
              ? (filteredSessions.reduce((sum, s) => sum + s.accuracy, 0) / filteredSessions.length).toFixed(1)
              : 0}%
          </span>
        </div>
        <div className="stat">
          <span className="label">Best Speed:</span>
          <span className="value">
            {Math.max(...filteredSessions.map(s => s.cardsPerMinute), 0).toFixed(0)} CPM
          </span>
        </div>
      </div>
    </div>
  );
}
```

### 6.6 DrillConfiguration Component

Create `src/components/drills/DrillConfiguration.tsx`:

```typescript
/**
 * Component for configuring drill settings
 */
interface DrillConfigurationProps {
  config: DrillConfig;
  onChange: (config: DrillConfig) => void;
  onSave?: (name: string) => void;
}

export function DrillConfiguration({
  config,
  onChange,
  onSave
}: DrillConfigurationProps) {
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [configName, setConfigName] = useState('');

  // Handle configuration changes
  const updateConfig = (updates: Partial<DrillConfig>) => {
    onChange({ ...config, ...updates });
  };

  // Handle saving configuration
  const handleSave = () => {
    if (onSave && configName.trim()) {
      onSave(configName.trim());
      setSaveDialogOpen(false);
      setConfigName('');
    }
  };

  return (
    <div className="drill-configuration">
      <h3>Drill Configuration</h3>

      <div className="config-section">
        <label>Drill Type</label>
        <select
          value={config.type}
          onChange={(e) => updateConfig({ type: e.target.value as DrillType })}
        >
          <option value="runningCount">Running Count</option>
          <option value="trueCount">True Count</option>
          <option value="speed">Speed Training</option>
          <option value="distraction">Distraction Training</option>
          <option value="deckEstimation">Deck Estimation</option>
          <option value="spotCheck">Spot Check</option>
        </select>
      </div>

      <div className="config-section">
        <label>Difficulty</label>
        <select
          value={config.difficulty}
          onChange={(e) => updateConfig({ difficulty: e.target.value as DrillDifficulty })}
        >
          <option value="beginner">Beginner</option>
          <option value="intermediate">Intermediate</option>
          <option value="advanced">Advanced</option>
          <option value="expert">Expert</option>
        </select>
      </div>

      <div className="config-section">
        <label>Duration (seconds)</label>
        <input
          type="number"
          value={config.duration}
          onChange={(e) => updateConfig({ duration: parseInt(e.target.value) || 0 })}
          min="0"
          max="600"
        />
        <small>Set to 0 for unlimited time</small>
      </div>

      <div className="config-section">
        <label>Card Speed</label>
        <select
          value={config.cardSpeed}
          onChange={(e) => updateConfig({ cardSpeed: e.target.value as CardSpeed })}
        >
          <option value="slow">Slow (2s per card)</option>
          <option value="medium">Medium (1s per card)</option>
          <option value="fast">Fast (0.5s per card)</option>
          <option value="expert">Expert (0.25s per card)</option>
        </select>
      </div>

      <div className="config-section">
        <label>Number of Decks</label>
        <input
          type="number"
          value={config.deckCount}
          onChange={(e) => updateConfig({ deckCount: parseInt(e.target.value) || 1 })}
          min="1"
          max="8"
        />
      </div>

      <div className="config-section">
        <label>
          <input
            type="checkbox"
            checked={config.showHints}
            onChange={(e) => updateConfig({ showHints: e.target.checked })}
          />
          Show Hints
        </label>
      </div>

      {/* Drill-specific options */}
      {config.type === 'runningCount' && (
        <div className="config-section">
          <label>Pause After Cards</label>
          <input
            type="number"
            value={config.options.pauseAfterCards || 5}
            onChange={(e) => updateConfig({
              options: {
                ...config.options,
                pauseAfterCards: parseInt(e.target.value) || 5
              }
            })}
            min="1"
            max="10"
          />
        </div>
      )}

      {config.type === 'speed' && (
        <div className="config-section">
          <label>Target Speed (CPM)</label>
          <input
            type="number"
            value={config.options.targetSpeed || 60}
            onChange={(e) => updateConfig({
              options: {
                ...config.options,
                targetSpeed: parseInt(e.target.value) || 60
              }
            })}
            min="30"
            max="200"
          />
        </div>
      )}

      {onSave && (
        <div className="config-actions">
          <button onClick={() => setSaveDialogOpen(true)}>
            Save Configuration
          </button>
        </div>
      )}

      {/* Save dialog */}
      {saveDialogOpen && (
        <div className="save-dialog">
          <div className="dialog-content">
            <h4>Save Configuration</h4>
            <input
              type="text"
              placeholder="Configuration name"
              value={configName}
              onChange={(e) => setConfigName(e.target.value)}
            />
            <div className="dialog-actions">
              <button onClick={handleSave} disabled={!configName.trim()}>
                Save
              </button>
              <button onClick={() => setSaveDialogOpen(false)}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

## 7. Integration with Existing Codebase

### 7.1 Navigation Integration

Update the main navigation to include the Drills Mode:

**File: `src/components/layout/Navigation.tsx`**

```typescript
// Add to existing navigation items
const navigationItems = [
  { href: '/', label: 'Practice', icon: PlayIcon },
  { href: '/drills', label: 'Drills', icon: TargetIcon }, // New item
  { href: '/strategy', label: 'Strategy', icon: BookIcon },
  { href: '/stats', label: 'Statistics', icon: ChartIcon },
  { href: '/settings', label: 'Settings', icon: CogIcon }
];
```

### 7.2 Shared Utilities Integration

Ensure drills use existing card and game utilities:

**File: `src/lib/drills/drills-engine.ts`**

```typescript
// Import existing utilities
import { createDeck, shuffleDeck } from '../game/deck';
import { getCardValue } from '../game/card-values';
import { Card } from '../../types/game';
import { generateUniqueId } from '../utils/id-generator';
```

### 7.3 Storage Integration

Integrate with existing local storage system:

**File: `src/hooks/useDrills.ts`**

```typescript
// Add storage integration
import { useLocalStorage } from './useLocalStorage';

export function useDrills() {
  // Use existing storage hook for persistence
  const [savedConfigs, setSavedConfigs] = useLocalStorage<SavedDrillConfig[]>('drill-configs', []);
  const [performanceHistory, setPerformanceHistory] = useLocalStorage<DrillPerformanceHistory>(
    'drill-performance',
    {
      sessions: [],
      byDrillType: {} as Record<DrillType, DrillTypePerformance>,
      badges: [],
      totalPoints: 0,
      level: 1
    }
  );

  // Rest of implementation...
}
```

### 7.4 Theme Integration

Ensure drills components use the existing theme system:

**File: `src/components/drills/DrillSelector.tsx`**

```typescript
// Use existing theme classes and components
import { Button } from '../ui/Button';
import { Card as UICard } from '../ui/Card';
import { Select } from '../ui/Select';
```

## 8. Testing Strategy

### 8.1 Unit Tests

Create comprehensive unit tests for core functionality:

**File: `src/lib/drills/__tests__/drills-engine.test.ts`**

```typescript
import { DrillsEngine } from '../drills-engine';
import { DRILL_CONSTANTS } from '../../constants';

describe('DrillsEngine', () => {
  let engine: DrillsEngine;

  beforeEach(() => {
    engine = new DrillsEngine(DRILL_CONSTANTS.DEFAULT_CONFIGS.RUNNING_COUNT_BEGINNER);
  });

  describe('initialization', () => {
    it('should initialize with correct default values', () => {
      expect(engine.isActive).toBe(false);
      expect(engine.runningCount).toBe(0);
      expect(engine.trueCount).toBe(0);
    });

    it('should create deck with correct number of cards', () => {
      expect(engine.cards.length).toBe(52); // Single deck
    });
  });

  describe('drill session management', () => {
    it('should start drill session correctly', () => {
      engine.start();
      expect(engine.isActive).toBe(true);
      expect(engine.startTime).toBeGreaterThan(0);
    });

    it('should end drill session correctly', () => {
      engine.start();
      engine.end();
      expect(engine.isActive).toBe(false);
    });

    it('should handle timed drills', (done) => {
      const config = { ...DRILL_CONSTANTS.DEFAULT_CONFIGS.RUNNING_COUNT_BEGINNER, duration: 1 };
      const timedEngine = new DrillsEngine(config);

      timedEngine.start();
      expect(timedEngine.isActive).toBe(true);

      setTimeout(() => {
        expect(timedEngine.isActive).toBe(false);
        done();
      }, 1100);
    });
  });

  describe('card counting', () => {
    it('should update running count correctly', () => {
      engine.start();
      const cards = engine.getNextCards(3);

      // Verify count is updated based on card values
      const expectedCount = cards?.reduce((sum, card) => sum + getCardValue(card), 0) || 0;
      expect(engine.runningCount).toBe(expectedCount);
    });

    it('should calculate true count correctly', () => {
      engine.start();
      engine.getNextCards(26); // Half deck

      const expectedTrueCount = Math.round((engine.runningCount / 0.5) * 2) / 2;
      expect(engine.trueCount).toBe(expectedTrueCount);
    });
  });

  describe('interaction recording', () => {
    it('should record correct interactions', () => {
      engine.start();
      const cards = engine.getNextCards(1);
      const result = engine.recordInteraction(engine.runningCount);

      expect(result.isCorrect).toBe(true);
      expect(result.userCount).toBe(engine.runningCount);
      expect(result.expectedCount).toBe(engine.runningCount);
    });

    it('should record incorrect interactions', () => {
      engine.start();
      engine.getNextCards(1);
      const result = engine.recordInteraction(999);

      expect(result.isCorrect).toBe(false);
      expect(result.userCount).toBe(999);
    });
  });

  describe('results calculation', () => {
    it('should calculate accuracy correctly', () => {
      engine.start();

      // Record some interactions
      engine.getNextCards(1);
      engine.recordInteraction(engine.runningCount); // Correct
      engine.getNextCards(1);
      engine.recordInteraction(999); // Incorrect

      engine.end();
      const result = engine.getResult();

      expect(result.accuracy).toBe(50); // 1 correct out of 2
    });

    it('should calculate cards per minute correctly', () => {
      engine.start();

      // Simulate processing cards
      for (let i = 0; i < 10; i++) {
        engine.getNextCards(1);
        engine.recordInteraction(engine.runningCount);
      }

      engine.end();
      const result = engine.getResult();

      expect(result.cardsPerMinute).toBeGreaterThan(0);
    });
  });
});
```

### 8.2 Integration Tests

**File: `src/hooks/__tests__/useDrills.test.ts`**

```typescript
import { renderHook, act } from '@testing-library/react';
import { useDrills } from '../useDrills';

describe('useDrills', () => {
  it('should initialize with default configuration', () => {
    const { result } = renderHook(() => useDrills());

    expect(result.current.config.type).toBe('runningCount');
    expect(result.current.config.difficulty).toBe('beginner');
    expect(result.current.isActive).toBe(false);
  });

  it('should start and end drills correctly', () => {
    const { result } = renderHook(() => useDrills());

    act(() => {
      result.current.startDrill();
    });

    expect(result.current.isActive).toBe(true);

    act(() => {
      result.current.endDrill();
    });

    expect(result.current.isActive).toBe(false);
    expect(result.current.results).toBeTruthy();
  });

  it('should save and load configurations', () => {
    const { result } = renderHook(() => useDrills());

    act(() => {
      result.current.setConfig({
        ...result.current.config,
        difficulty: 'expert'
      });
    });

    act(() => {
      result.current.saveConfig('My Expert Config');
    });

    expect(result.current.savedConfigs).toHaveLength(1);
    expect(result.current.savedConfigs[0].name).toBe('My Expert Config');
    expect(result.current.savedConfigs[0].difficulty).toBe('expert');
  });
});
```

### 8.3 Component Tests

**File: `src/components/drills/__tests__/DrillSelector.test.tsx`**

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { DrillSelector } from '../DrillSelector';
import { DRILL_CONSTANTS } from '../../../lib/constants';

const mockProps = {
  config: DRILL_CONSTANTS.DEFAULT_CONFIGS.RUNNING_COUNT_BEGINNER,
  setConfig: jest.fn(),
  savedConfigs: [],
  performanceHistory: {
    sessions: [],
    byDrillType: {},
    badges: [],
    totalPoints: 0,
    level: 1
  },
  onStart: jest.fn(),
  onSave: jest.fn(),
  onLoad: jest.fn(),
  onDelete: jest.fn()
};

describe('DrillSelector', () => {
  it('should render drill type options', () => {
    render(<DrillSelector {...mockProps} />);

    expect(screen.getByText('Running Count')).toBeInTheDocument();
    expect(screen.getByText('True Count')).toBeInTheDocument();
    expect(screen.getByText('Speed Training')).toBeInTheDocument();
  });

  it('should call onStart when start button is clicked', () => {
    render(<DrillSelector {...mockProps} />);

    const startButton = screen.getByText('Start Drill');
    fireEvent.click(startButton);

    expect(mockProps.onStart).toHaveBeenCalled();
  });

  it('should update configuration when drill type changes', () => {
    render(<DrillSelector {...mockProps} />);

    const drillTypeSelect = screen.getByLabelText('Drill Type');
    fireEvent.change(drillTypeSelect, { target: { value: 'trueCount' } });

    expect(mockProps.setConfig).toHaveBeenCalledWith(
      expect.objectContaining({ type: 'trueCount' })
    );
  });
});
```

### 8.4 E2E Tests

**File: `cypress/e2e/drills.cy.ts`**

```typescript
describe('Drills Mode', () => {
  beforeEach(() => {
    cy.visit('/drills');
  });

  it('should complete a basic running count drill', () => {
    // Select running count drill
    cy.get('[data-testid="drill-type-select"]').select('runningCount');
    cy.get('[data-testid="difficulty-select"]').select('beginner');

    // Start drill
    cy.get('[data-testid="start-drill-button"]').click();

    // Verify drill is active
    cy.get('[data-testid="active-drill"]').should('be.visible');
    cy.get('[data-testid="card-display"]').should('be.visible');

    // Submit a count
    cy.get('[data-testid="count-input"]').type('1');
    cy.get('[data-testid="submit-count-button"]').click();

    // Verify feedback
    cy.get('[data-testid="interaction-feedback"]').should('be.visible');

    // End drill
    cy.get('[data-testid="end-drill-button"]').click();

    // Verify results
    cy.get('[data-testid="drill-results"]').should('be.visible');
    cy.get('[data-testid="accuracy-score"]').should('contain', '%');
    cy.get('[data-testid="speed-score"]').should('contain', 'CPM');
  });

  it('should save and load drill configurations', () => {
    // Configure drill
    cy.get('[data-testid="drill-type-select"]').select('trueCount');
    cy.get('[data-testid="difficulty-select"]').select('advanced');
    cy.get('[data-testid="duration-input"]').clear().type('180');

    // Save configuration
    cy.get('[data-testid="save-config-button"]').click();
    cy.get('[data-testid="config-name-input"]').type('My Advanced Config');
    cy.get('[data-testid="save-dialog-confirm"]').click();

    // Verify saved
    cy.get('[data-testid="saved-configs"]').should('contain', 'My Advanced Config');

    // Load configuration
    cy.get('[data-testid="config-My Advanced Config"]').click();
    cy.get('[data-testid="load-config-button"]').click();

    // Verify loaded
    cy.get('[data-testid="drill-type-select"]').should('have.value', 'trueCount');
    cy.get('[data-testid="difficulty-select"]').should('have.value', 'advanced');
    cy.get('[data-testid="duration-input"]').should('have.value', '180');
  });

  it('should display performance history', () => {
    // Complete a drill first
    cy.get('[data-testid="start-drill-button"]').click();
    cy.get('[data-testid="count-input"]').type('0');
    cy.get('[data-testid="submit-count-button"]').click();
    cy.get('[data-testid="end-drill-button"]').click();

    // Navigate to performance
    cy.get('[data-testid="performance-tab"]').click();

    // Verify performance data
    cy.get('[data-testid="performance-chart"]').should('be.visible');
    cy.get('[data-testid="session-history"]').should('contain', 'Running Count');
    cy.get('[data-testid="total-points"]').should('be.visible');
  });
});
```

## 9. Performance Considerations

### 9.1 Memory Management

- **Card Generation**: Pre-generate and cache shuffled decks to avoid repeated shuffling
- **Result Storage**: Implement pagination for drill history to prevent memory bloat
- **Component Optimization**: Use React.memo for expensive chart components

```typescript
// Optimized card generation
class DeckCache {
  private static cache = new Map<string, Card[]>();

  static getDeck(deckCount: number, seed?: string): Card[] {
    const key = `${deckCount}-${seed || 'default'}`;

    if (!this.cache.has(key)) {
      const deck = createDeck(deckCount);
      this.cache.set(key, shuffleDeck(deck, seed));
    }

    return [...this.cache.get(key)!]; // Return copy
  }
}
```

### 9.2 Rendering Optimization

- **Virtual Scrolling**: For large performance history lists
- **Debounced Updates**: For real-time performance metrics
- **Lazy Loading**: For chart components and heavy visualizations

```typescript
// Optimized performance chart
const PerformanceChart = React.memo(({ data }: { data: ChartData[] }) => {
  const chartData = useMemo(() => processChartData(data), [data]);

  return (
    <Suspense fallback={<ChartSkeleton />}>
      <LazyChart data={chartData} />
    </Suspense>
  );
});
```

### 9.3 Storage Optimization

- **Compression**: Compress drill results before storing in localStorage
- **Cleanup**: Automatically remove old drill sessions beyond a threshold
- **Indexing**: Create efficient indexes for performance queries

```typescript
// Storage optimization
class DrillStorage {
  private static readonly MAX_SESSIONS = 1000;

  static saveSession(session: DrillResult): void {
    const sessions = this.getSessions();
    sessions.push(session);

    // Keep only recent sessions
    if (sessions.length > this.MAX_SESSIONS) {
      sessions.splice(0, sessions.length - this.MAX_SESSIONS);
    }

    // Compress before storing
    const compressed = LZString.compress(JSON.stringify(sessions));
    localStorage.setItem('drill-sessions', compressed);
  }

  static getSessions(): DrillResult[] {
    const compressed = localStorage.getItem('drill-sessions');
    if (!compressed) return [];

    const decompressed = LZString.decompress(compressed);
    return JSON.parse(decompressed || '[]');
  }
}
```

## 10. Deployment and Rollout Plan

### 10.1 Development Phases

**Phase 1: Core Implementation (Week 1-2)**
- Implement data structures and types
- Create DrillsEngine class
- Build useDrills hook
- Basic UI components

**Phase 2: UI Polish (Week 3)**
- Complete all UI components
- Add styling and animations
- Implement responsive design
- Add accessibility features

**Phase 3: Integration (Week 4)**
- Integrate with existing codebase
- Add navigation and routing
- Implement storage persistence
- Performance optimization

**Phase 4: Testing (Week 5)**
- Unit test coverage
- Integration testing
- E2E test scenarios
- Performance testing

**Phase 5: Beta Release (Week 6)**
- Internal testing
- Bug fixes and refinements
- Documentation updates
- Preparation for production

### 10.2 Feature Flags

Implement feature flags for gradual rollout:

```typescript
// Feature flag configuration
export const FEATURE_FLAGS = {
  DRILLS_MODE: {
    enabled: process.env.NODE_ENV === 'development' ||
             process.env.ENABLE_DRILLS === 'true',
    variants: {
      BASIC_DRILLS: true,
      ADVANCED_ANALYTICS: false,
      MULTIPLAYER_DRILLS: false
    }
  }
} as const;

// Usage in components
function Navigation() {
  const showDrills = useFeatureFlag('DRILLS_MODE');

  return (
    <nav>
      {/* Other nav items */}
      {showDrills && (
        <NavItem href="/drills" label="Drills" />
      )}
    </nav>
  );
}
```

### 10.3 Monitoring and Analytics

Add monitoring for drill usage and performance:

```typescript
// Analytics tracking
class DrillAnalytics {
  static trackDrillStart(config: DrillConfig): void {
    analytics.track('drill_started', {
      drill_type: config.type,
      difficulty: config.difficulty,
      duration: config.duration,
      deck_count: config.deckCount
    });
  }

  static trackDrillComplete(result: DrillResult): void {
    analytics.track('drill_completed', {
      drill_type: result.config.type,
      accuracy: result.accuracy,
      cards_per_minute: result.cardsPerMinute,
      duration: new Date(result.endTime).getTime() - new Date(result.startTime).getTime(),
      badges_earned: result.badgesEarned.length
    });
  }

  static trackPerformanceImprovement(
    previousBest: number,
    currentScore: number,
    metric: 'accuracy' | 'speed'
  ): void {
    if (currentScore > previousBest) {
      analytics.track('performance_improvement', {
        metric,
        previous_best: previousBest,
        current_score: currentScore,
        improvement: currentScore - previousBest
      });
    }
  }
}
```

### 10.4 Documentation Updates

Update user-facing documentation:

1. **User Guide**: Add drills mode section to existing user documentation
2. **Tutorial**: Create interactive tutorial for first-time drill users
3. **FAQ**: Add common questions about drill functionality
4. **API Documentation**: Document new types and interfaces for future extensions

### 10.5 Success Metrics

Define success criteria for the drills feature:

- **Engagement**: 60% of active users try drills mode within first month
- **Retention**: 40% of drill users return for second session within a week
- **Performance**: Average user accuracy improvement of 15% after 10 drill sessions
- **Technical**: Page load time under 2 seconds, 99.9% uptime
- **User Satisfaction**: Average rating of 4.5+ stars in user feedback

## 11. Future Enhancements

### 11.1 Advanced Drill Types

- **Multiplayer Drills**: Compete with other users in real-time
- **Adaptive Difficulty**: AI-powered difficulty adjustment based on performance
- **Custom Scenarios**: User-created drill scenarios and challenges
- **Team Challenges**: Group competitions and leaderboards

### 11.2 Enhanced Analytics

- **Detailed Performance Insights**: Heat maps, trend analysis, weakness identification
- **Comparative Analytics**: Compare performance with other users (anonymized)
- **Predictive Modeling**: Predict optimal practice schedules and drill types
- **Export Capabilities**: Export performance data for external analysis

### 11.3 Gamification Expansion

- **Achievement System**: Expanded badge collection with rare achievements
- **Progression Paths**: Structured learning paths with milestones
- **Social Features**: Share achievements, challenge friends
- **Seasonal Events**: Special drill challenges and limited-time rewards

This completes the Technical Requirements Document for the Drills Mode feature. The document now provides comprehensive coverage of all technical aspects needed for successful implementation.
