# Technical Requirements Document: Drills Mode

## 1. Technical Overview

The Drills Mode feature will enhance the Stack Advantage blackjack trainer by providing focused practice exercises specifically designed to improve card counting speed and accuracy. This document outlines the technical implementation details required to deliver this feature, focusing on architecture, data structures, components, and integration points.

## 2. Architecture and Implementation Approach

### 2.1 Component Architecture

The implementation will follow the existing architecture pattern:
- Create a new `DrillsEngine` class to handle drill logic and state management
- Define new types and interfaces for drill configuration and results
- Create new UI components for drill selection, execution, and results display
- Implement a new `useDrills` hook for React state management
- Integrate with existing game components where appropriate

### 2.2 Implementation Strategy

1. Define data structures for drills configuration and results
2. Implement the core `DrillsEngine` class
3. Create the `useDrills` React hook
4. Develop UI components for the drills experience
5. Implement performance tracking and storage
6. Add educational content and tooltips
7. Integrate with the main application
8. Implement automated testing

## 3. Data Structure Modifications

### 3.1 Drill Types and Configuration

Create new types in `src/types/drills.ts`:

```typescript
/** Available drill types */
export type DrillType = 
  | 'runningCount' 
  | 'trueCount' 
  | 'speed' 
  | 'distraction' 
  | 'deckEstimation'
  | 'spotCheck';

/** Difficulty levels for drills */
export type DrillDifficulty = 'beginner' | 'intermediate' | 'advanced' | 'expert';

/** Speed settings for card display */
export type CardSpeed = 'slow' | 'medium' | 'fast' | 'expert';

/** Configuration for a drill session */
export interface DrillConfig {
  /** Type of drill */
  type: DrillType;
  
  /** Difficulty level */
  difficulty: DrillDifficulty;
  
  /** Duration in seconds (0 for non-timed drills) */
  duration: number;
  
  /** Speed of card display */
  cardSpeed: CardSpeed;
  
  /** Whether to show visual hints */
  showHints: boolean;
  
  /** Number of decks to use */
  deckCount: number;
  
  /** Custom options specific to each drill type */
  options: Record<string, any>;
}

/** Saved drill configuration with name */
export interface SavedDrillConfig extends DrillConfig {
  /** User-defined name for this configuration */
  name: string;
  
  /** Date when this configuration was saved */
  savedAt: string;
}
```

### 3.2 Drill Results and Performance

Add types for tracking drill results:

```typescript
/** Results from a single drill session */
export interface DrillResult {
  /** ID of this drill session */
  id: string;
  
  /** Configuration used for this session */
  config: DrillConfig;
  
  /** When the drill was started */
  startTime: string;
  
  /** When the drill was completed */
  endTime: string;
  
  /** Overall accuracy percentage */
  accuracy: number;
  
  /** Cards processed per minute */
  cardsPerMinute: number;
  
  /** Average response time in milliseconds */
  avgResponseTime: number;
  
  /** Detailed results for each interaction */
  details: DrillInteractionResult[];
  
  /** Achievement badges earned in this session */
  badgesEarned: string[];
  
  /** Points earned in this session */
  pointsEarned: number;
}

/** Result of a single interaction within a drill */
export interface DrillInteractionResult {
  /** Timestamp of this interaction */
  timestamp: number;
  
  /** Cards shown in this interaction */
  cards: Card[];
  
  /** Expected count after these cards */
  expectedCount: number;
  
  /** User's count input */
  userCount: number;
  
  /** Whether the user's input was correct */
  isCorrect: boolean;
  
  /** Response time in milliseconds */
  responseTime: number;
}

/** User's drill performance history */
export interface DrillPerformanceHistory {
  /** Results of all completed drill sessions */
  sessions: DrillResult[];
  
  /** Performance metrics by drill type */
  byDrillType: Record<DrillType, DrillTypePerformance>;
  
  /** Achievement badges earned */
  badges: string[];
  
  /** Total points earned across all drills */
  totalPoints: number;
  
  /** Current user level */
  level: number;
}

/** Performance metrics for a specific drill type */
export interface DrillTypePerformance {
  /** Number of sessions completed */
  sessionsCompleted: number;
  
  /** Best accuracy achieved */
  bestAccuracy: number;
  
  /** Best speed achieved (cards per minute) */
  bestSpeed: number;
  
  /** Average accuracy across all sessions */
  avgAccuracy: number;
  
  /** Average speed across all sessions */
  avgSpeed: number;
  
  /** Date of last session */
  lastPlayed: string;
}
```

### 3.3 Constants Updates

Add drill-related constants in `src/lib/constants.ts`:

```typescript
export const DRILL_CONSTANTS = {
  /** Card display speeds in milliseconds */
  CARD_SPEEDS: {
    SLOW: 2000,
    MEDIUM: 1000,
    FAST: 500,
    EXPERT: 250
  },
  
  /** Default durations in seconds */
  DEFAULT_DURATIONS: {
    BEGINNER: 60,
    INTERMEDIATE: 120,
    ADVANCED: 180,
    EXPERT: 300
  },
  
  /** Points awarded for different achievements */
  POINTS: {
    PERFECT_ACCURACY: 100,
    SPEED_THRESHOLD: 50,
    COMPLETION: 10,
    CORRECT_ANSWER: 1
  },
  
  /** Achievement badge definitions */
  BADGES: {
    PERFECT_NOVICE: {
      id: 'perfect_novice',
      name: 'Perfect Novice',
      description: 'Complete a beginner drill with 100% accuracy',
      icon: '🎯'
    },
    SPEED_DEMON: {
      id: 'speed_demon',
      name: 'Speed Demon',
      description: 'Process more than 100 cards per minute with at least 90% accuracy',
      icon: '⚡'
    },
    // Additional badges...
  },
  
  /** Default drill configurations */
  DEFAULT_CONFIGS: {
    RUNNING_COUNT_BEGINNER: {
      type: 'runningCount' as DrillType,
      difficulty: 'beginner' as DrillDifficulty,
      duration: 60,
      cardSpeed: 'slow' as CardSpeed,
      showHints: true,
      deckCount: 1,
      options: {
        pauseAfterCards: 5
      }
    },
    // Additional default configurations...
  }
} as const;
```

## 4. DrillsEngine Class

Create a new class in `src/lib/drills/drills-engine.ts`:

```typescript
/**
 * Core engine for managing drill sessions
 */
export class DrillsEngine {
  /** Current drill configuration */
  private config: DrillConfig;
  
  /** Cards to be used in this drill */
  private cards: Card[];
  
  /** Current position in the card sequence */
  private currentPosition: number;
  
  /** Current running count */
  private runningCount: number;
  
  /** Current true count */
  private trueCount: number;
  
  /** Remaining decks */
  private remainingDecks: number;
  
  /** Start time of the current drill */
  private startTime: number;
  
  /** Results of interactions in this drill */
  private interactionResults: DrillInteractionResult[];
  
  /** Whether the drill is currently active */
  private isActive: boolean;
  
  /** Timer ID for timed drills */
  private timerId: number | null;
  
  /**
   * Creates a new DrillsEngine instance
   * @param config Initial drill configuration
   */
  constructor(config: DrillConfig) {
    this.config = { ...config };
    this.cards = [];
    this.currentPosition = 0;
    this.runningCount = 0;
    this.trueCount = 0;
    this.remainingDecks = config.deckCount;
    this.startTime = 0;
    this.interactionResults = [];
    this.isActive = false;
    this.timerId = null;
    
    // Initialize cards for the drill
    this.initializeCards();
  }
  
  /**
   * Initializes the card deck(s) for this drill
   * @private
   */
  private initializeCards(): void {
    // Create and shuffle deck(s) based on configuration
    const deck = createDeck(this.config.deckCount);
    this.cards = shuffleDeck(deck);
    
    // For certain drill types, we may want to arrange cards in a specific way
    if (this.config.type === 'spotCheck') {
      this.arrangeSpotCheckCards();
    }
  }
  
  /**
   * Starts the drill session
   * @public
   */
  public start(): void {
    if (this.isActive) return;
    
    this.isActive = true;
    this.startTime = Date.now();
    this.interactionResults = [];
    this.currentPosition = 0;
    this.runningCount = 0;
    this.trueCount = 0;
    this.remainingDecks = this.config.deckCount;
    
    // Set up timer for timed drills
    if (this.config.duration > 0) {
      this.timerId = window.setTimeout(() => this.end(), this.config.duration * 1000);
    }
  }
  
  /**
   * Ends the current drill session
   * @public
   */
  public end(): void {
    if (!this.isActive) return;
    
    this.isActive = false;
    
    // Clear timer if it exists
    if (this.timerId !== null) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
  }
  
  /**
   * Gets the next card(s) to display
   * @param count Number of cards to get
   * @returns Array of cards or null if drill is complete
   * @public
   */
  public getNextCards(count: number = 1): Card[] | null {
    if (!this.isActive || this.currentPosition >= this.cards.length) {
      return null;
    }
    
    const endPosition = Math.min(this.currentPosition + count, this.cards.length);
    const cards = this.cards.slice(this.currentPosition, endPosition);
    this.currentPosition = endPosition;
    
    // Update counts
    this.updateCounts(cards);
    
    // Check if we've reached the end
    if (this.currentPosition >= this.cards.length) {
      this.end();
    }
    
    return cards;
  }
  
  /**
   * Updates running count and true count based on cards
   * @param cards Cards to count
   * @private
   */
  private updateCounts(cards: Card[]): void {
    // Update running count
    for (const card of cards) {
      this.runningCount += getCardValue(card);
    }
    
    // Update remaining decks and true count
    this.remainingDecks = (this.cards.length - this.currentPosition) / 52;
    this.trueCount = this.remainingDecks > 0 
      ? Math.round((this.runningCount / this.remainingDecks) * 2) / 2 
      : this.runningCount;
  }
  
  /**
   * Records a user interaction result
   * @param userCount User's count input
   * @returns Result of this interaction
   * @public
   */
  public recordInteraction(userCount: number): DrillInteractionResult {
    const expectedCount = this.config.type === 'trueCount' ? this.trueCount : this.runningCount;
    const isCorrect = userCount === expectedCount;
    const responseTime = Date.now() - this.startTime;
    
    // Get cards shown in this interaction
    const interactionCards = this.getInteractionCards();
    
    const result: DrillInteractionResult = {
      timestamp: Date.now(),
      cards: interactionCards,
      expectedCount,
      userCount,
      isCorrect,
      responseTime
    };
    
    this.interactionResults.push(result);
    return result;
  }
  
  /**
   * Gets the cards shown in the current interaction
   * @returns Array of cards
   * @private
   */
  private getInteractionCards(): Card[] {
    // Implementation depends on drill type
    // For most drills, this will be the cards since the last interaction
    // For spot check drills, this might be a specific subset
    return [];
  }
  
  /**
   * Gets the current drill result
   * @returns Current drill result
   * @public
   */
  public getResult(): DrillResult {
    const endTime = this.isActive ? Date.now() : this.startTime;
    const durationMs = endTime - this.startTime;
    const durationMinutes = durationMs / 60000;
    
    // Calculate accuracy
    const correctInteractions = this.interactionResults.filter(r => r.isCorrect).length;
    const accuracy = this.interactionResults.length > 0
      ? (correctInteractions / this.interactionResults.length) * 100
      : 0;
    
    // Calculate cards per minute
    const totalCards = this.interactionResults.reduce((sum, r) => sum + r.cards.length, 0);
    const cardsPerMinute = durationMinutes > 0 ? totalCards / durationMinutes : 0;
    
    // Calculate average response time
    const totalResponseTime = this.interactionResults.reduce((sum, r) => sum + r.responseTime, 0);
    const avgResponseTime = this.interactionResults.length > 0
      ? totalResponseTime / this.interactionResults.length
      : 0;
    
    // Determine badges earned
    const badgesEarned = this.calculateEarnedBadges(accuracy, cardsPerMinute);
    
    // Calculate points earned
    const pointsEarned = this.calculatePointsEarned(accuracy, cardsPerMinute, correctInteractions);
    
    return {
      id: generateUniqueId(),
      config: this.config,
      startTime: new Date(this.startTime).toISOString(),
      endTime: new Date(endTime).toISOString(),
      accuracy,
      cardsPerMinute,
      avgResponseTime,
      details: [...this.interactionResults],
      badgesEarned,
      pointsEarned
    };
  }
  
  /**
   * Calculates badges earned in this session
   * @param accuracy Accuracy percentage
   * @param cardsPerMinute Cards processed per minute
   * @returns Array of badge IDs earned
   * @private
   */
  private calculateEarnedBadges(accuracy: number, cardsPerMinute: number): string[] {
    const badges: string[] = [];
    
    // Perfect accuracy badge
    if (accuracy === 100 && this.interactionResults.length >= 10) {
      if (this.config.difficulty === 'beginner') {
        badges.push('perfect_novice');
      } else if (this.config.difficulty === 'intermediate') {
        badges.push('perfect_intermediate');
      } else if (this.config.difficulty === 'advanced') {
        badges.push('perfect_advanced');
      } else if (this.config.difficulty === 'expert') {
        badges.push('perfect_expert');
      }
    }
    
    // Speed badge
    if (cardsPerMinute >= 100 && accuracy >= 90) {
      badges.push('speed_demon');
    }
    
    return badges;
  }
  
  /**
   * Calculates points earned in this session
   * @param accuracy Accuracy percentage
   * @param cardsPerMinute Cards processed per minute
   * @param correctAnswers Number of correct answers
   * @returns Total points earned
   * @private
   */
  private calculatePointsEarned(
    accuracy: number, 
    cardsPerMinute: number, 
    correctAnswers: number
  ): number {
    let points = 0;
    
    // Points for completion
    points += DRILL_CONSTANTS.POINTS.COMPLETION;
    
    // Points for correct answers
    points += correctAnswers * DRILL_CONSTANTS.POINTS.CORRECT_ANSWER;
    
    // Points for perfect accuracy
    if (accuracy === 100 && this.interactionResults.length >= 10) {
      points += DRILL_CONSTANTS.POINTS.PERFECT_ACCURACY;
    }
    
    // Points for speed
    if (cardsPerMinute >= 100 && accuracy >= 90) {
      points += DRILL_CONSTANTS.POINTS.SPEED_THRESHOLD;
    }
    
    return points;
  }
}
```

## 5. React Hook Implementation

Create a new hook in `src/hooks/useDrills.ts`:

```typescript
/**
 * React hook for managing drill state
 */
export function useDrills() {
  // State for drill engine
  const [drillEngine, setDrillEngine] = useState<DrillsEngine | null>(null);
  
  // State for current drill configuration
  const [config, setConfig] = useState<DrillConfig>(DRILL_CONSTANTS.DEFAULT_CONFIGS.RUNNING_COUNT_BEGINNER);
  
  // State for drill status
  const [isActive, setIsActive] = useState(false);
  
  // State for current cards being shown
  const [currentCards, setCurrentCards] = useState<Card[]>([]);
  
  // State for drill results
  const [results, setResults] = useState<DrillResult | null>(null);
  
  // State for saved configurations
  const [savedConfigs, setSavedConfigs] = useState<SavedDrillConfig[]>([]);
  
  // State for performance history
  const [performanceHistory, setPerformanceHistory] = useState<DrillPerformanceHistory>({
    sessions: [],
    byDrillType: {} as Record<DrillType, DrillTypePerformance>,
    badges: [],
    totalPoints: 0,
    level: 1
  });
  
  // Initialize drill engine when configuration changes
  useEffect(() => {
    if (!isActive && (!drillEngine || !isEqual(drillEngine.config, config))) {
      setDrillEngine(new DrillsEngine(config));
    }
  }, [config, isActive, drillEngine]);
  
  // Start a drill session
  const startDrill = useCallback(() => {
    if (!drillEngine) return;
    
    drillEngine.start();
    setIsActive(true);
    setResults(null);
    
    // Show first cards
    const cards = drillEngine.getNextCards(config.options.cardsPerRound || 1);
    if (cards) {
      setCurrentCards(cards);
    }
  }, [drillEngine, config]);
  
  // End a drill session
  const endDrill = useCallback(() => {
    if (!drillEngine || !isActive) return;
    
    drillEngine.end();
    setIsActive(false);
    
    // Get and save results
    const result = drillEngine.getResult();
    setResults(result);
    
    // Update performance history
    updatePerformanceHistory(result);
  }, [drillEngine, isActive]);
  
  // Submit user's count
  const submitCount = useCallback((userCount: number) => {
    if (!drillEngine || !isActive) return null;
    
    // Record interaction
    const result = drillEngine.recordInteraction(userCount);
    
    // Get next cards
    const nextCards = drillEngine.getNextCards(config.options.cardsPerRound || 1);
    if (nextCards) {
      setCurrentCards(nextCards);
    } else {
      // End drill if no more cards
      endDrill();
    }
    
    return result;
  }, [drillEngine, isActive, config, endDrill]);
  
  // Update performance history with new result
  const updatePerformanceHistory = useCallback((result: DrillResult) => {
    setPerformanceHistory(prev => {
      // Add session to history
      const sessions = [...prev.sessions, result];
      
      // Update drill type performance
      const drillType = result.config.type;
      const typePerformance = prev.byDrillType[drillType] || {
        sessionsCompleted: 0,
        bestAccuracy: 0,
        bestSpeed: 0,
        avgAccuracy: 0,
        avgSpeed: 0,
        lastPlayed: ''
      };
      
      const updatedTypePerformance: DrillTypePerformance = {
        sessionsCompleted: typePerformance.sessionsCompleted + 1,
        bestAccuracy: Math.max(typePerformance.bestAccuracy, result.accuracy),
        bestSpeed: Math.max(typePerformance.bestSpeed, result.cardsPerMinute),
        avgAccuracy: calculateAverage(
          typePerformance.avgAccuracy, 
          result.accuracy, 
          typePerformance.sessionsCompleted
        ),
        avgSpeed: calculateAverage(
          typePerformance.avgSpeed, 
          result.cardsPerMinute, 
          typePerformance.sessionsCompleted
        ),
        lastPlayed: result.endTime
      };
      
      // Update badges
      const badges = [...new Set([...prev.badges, ...result.badgesEarned])];
      
      // Update total points
      const totalPoints = prev.totalPoints + result.pointsEarned;
      
      // Calculate level based on points
      const level = Math.floor(totalPoints / 1000) + 1;
      
      return {
        sessions,
        byDrillType: {
          ...prev.byDrillType,
          [drillType]: updatedTypePerformance
        },
        badges,
        totalPoints,
        level
      };
    });
  }, []);
  
  // Save a drill configuration
  const saveConfig = useCallback((name: string) => {
    const savedConfig: SavedDrillConfig = {
      ...config,
      name,
      savedAt: new Date().toISOString()
    };
    
    setSavedConfigs(prev => [...prev, savedConfig]);
    return savedConfig;
  }, [config]);
  
  // Load a saved configuration
  const loadConfig = useCallback((savedConfig: SavedDrillConfig) => {
    setConfig(savedConfig);
  }, []);
  
  // Delete a saved configuration
  const deleteConfig = useCallback((savedConfig: SavedDrillConfig) => {
    setSavedConfigs(prev => prev.filter(c => c.name !== savedConfig.name));
  }, []);
  
  return {
    // State
    config,
    setConfig,
    isActive,
    currentCards,
    results,
    savedConfigs,
    performanceHistory,
    
    // Actions
    startDrill,
    endDrill,
    submitCount,
    saveConfig,
    loadConfig,
    deleteConfig
  };
}

/**
 * Helper function to calculate running average
 */
function calculateAverage(
  currentAvg: number, 
  newValue: number, 
  currentCount: number
): number {
  return ((currentAvg * currentCount) + newValue) / (currentCount + 1);
}
```

## 6. UI Components

### 6.1 DrillsPage Component

Create a new page component in `src/app/drills/page.tsx`:

```typescript
/**
 * Main page for Drills Mode
 */
export default function DrillsPage() {
  const {
    config,
    setConfig,
    isActive,
    currentCards,
    results,
    savedConfigs,
    performanceHistory,
    startDrill,
    endDrill,
    submitCount,
    saveConfig,
    loadConfig,
    deleteConfig
  } = useDrills();
  
  // State for user input
  const [userCount, setUserCount] = useState<string>('');
  
  // State for UI view
  const [view, setView] = useState<'select' | 'active' | 'results'>('select');
  
  // Effect to update view based on drill state
  useEffect(() => {
    if (isActive) {
      setView('active');
    } else if (results) {
      setView('results');
    } else {
      setView('select');
    }
  }, [isActive, results]);
  
  // Handle count submission
  const handleSubmitCount = () => {
    const count = parseInt(userCount);
    if (isNaN(count)) return;
    
    submitCount(count);
    setUserCount('');
  };
  
  // Render appropriate view
  return (
    <div className="drills-page">
      <h1>Card Counting Drills</h1>
      
      {view === 'select' && (
        <DrillSelector
          config={config}
          setConfig={setConfig}
          savedConfigs={savedConfigs}
          performanceHistory={performanceHistory}
          onStart={startDrill}
          onSave={saveConfig}
          onLoad={loadConfig}
          onDelete={deleteConfig}
        />
      )}
      
      {view === 'active' && (
        <ActiveDrill
          config={config}
          cards={currentCards}
          userCount={userCount}
          setUserCount={setUserCount}
          onSubmit={handleSubmitCount}
          onEnd={endDrill}
        />
      )}
      
      {view === 'results' && results && (
        <DrillResults
          results={results}
          performanceHistory={performanceHistory}
          onRestart={() => startDrill()}
          onNewDrill={() => setResults(null)}
        />
      )}
    </div>
  );
}
```

### 6.2 DrillSelector Component

Create `src/components/drills/DrillSelector.tsx`:

```typescript
/**
 * Component for selecting and configuring drills
 */
interface DrillSelectorProps {
  config: DrillConfig;
  setConfig: (config: DrillConfig) => void;
  savedConfigs: SavedDrillConfig[];
  performanceHistory: DrillPerformanceHistory;
  onStart: () => void;
  onSave: (name: string) => void;
  onLoad: (config: SavedDrillConfig) => void;
  onDelete: (config: SavedDrillConfig) => void;
}

export function DrillSelector({
  config,
  setConfig,
  savedConfigs,
  performanceHistory,
  onStart,
  onSave,
  onLoad,
  onDelete
}: DrillSelectorProps) {
  // Implementation details...
}
```

### 6.3 ActiveDrill Component

Create `src/components/drills/ActiveDrill.tsx`:

```typescript
/**
 * Component for the active drill session
 */
interface ActiveDrillProps {
  config: DrillConfig;
  cards: Card[];
  userCount: string;
  setUserCount: (value: string) => void;
  onSubmit: () => void;
  onEnd: () => void;
}

export function ActiveDrill({
  config,
  cards,
  userCount,
  setUserCount,
  onSubmit,
  onEnd
}: ActiveDrillProps) {
  // Implementation details...
}
```

### 6.4 DrillResults Component

Create `src/components/drills/DrillResults.tsx`:

```typescript
/**
 * Component for displaying drill results
 */
interface DrillResultsProps {
  results: DrillResult;
  performanceHistory: DrillPerformanceHistory;
  onRestart: () => void;
  onNewDrill: () => void;
}

export function DrillResults({
  results,
  performanceHistory,
  onRestart,
  onNewDrill
}: DrillResultsProps) {
  // Implementation details...
}
```

### 6.5
